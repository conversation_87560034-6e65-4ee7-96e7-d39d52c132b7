//
//  SettingsView.swift
//  cadence
//
//  Created by Assistant on 7/26/25.
//

import SwiftUI
import SwiftData
import MessageUI
import AuthenticationServices

struct SettingsView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var authManager = AuthenticationManager()
    @State private var showMenu = false
    @State private var showDatabaseTest = false
    @State private var showFAQ = false
    @State private var showAbout = false
    @State private var showMailComposer = false
    @State private var showRunRecord = false
    @State private var recentRuns: [RunRecord] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 设置标题
                
                Text("settings")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
                
                // Apple 登录区域
                VStack(spacing: 15) {
                    if authManager.isSignedIn {
                        // 已登录状态
                        VStack(spacing: 10) {
                            HStack {
                                Image(systemName: "person.circle.fill")
                                    .font(.title)
                                    .foregroundColor(.blue)
                                
                                VStack(alignment: .leading, spacing: 2) {
                                    if let userName = authManager.userName, !userName.isEmpty {
                                        Text(userName)
                                            .font(.headline)
                                            .foregroundColor(.primary)
                                    } else {
                                        Text("已登录")
                                            .font(.headline)
                                            .foregroundColor(.primary)
                                    }
                                    
                                    if let userEmail = authManager.userEmail {
                                        Text(userEmail)
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }
                                
                                Spacer()
                                
                                Button("退出") {
                                    authManager.signOut()
                                }
                                .font(.caption)
                                .foregroundColor(.red)
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(20)
                        }
                    } else {
                        // 未登录状态
                        Button(action: {
                            authManager.signInWithApple()
                        }) {
                            HStack {
                                Image(systemName: "applelogo")
                                    .font(.title2)
                                    .foregroundColor(.white)
                                
                                Text("使用 Apple 登录")
                                    .font(.headline)
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.black)
                            .cornerRadius(20)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal)
                Divider()
                    .padding(.horizontal)
                
                // 设置选项
                VStack(spacing: 20) {
                    // 历史记录按钮
                    // HistoryCell()
                    
                    
                    Button(action: {
                        showRunRecord = true
                    }) {
                        HStack {
                            Image(systemName: "list.bullet")
                                .font(.title2)
                                .foregroundColor(.orange)
                            Text("Run Records")
                                .font(.headline)
                                .foregroundColor(.primary)
                            Spacer()
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                    }
                }
                .padding(.horizontal)
                // 直接展示帮助相关按钮
                Button(action: {
                    showFAQ = true
                }) {
                    HStack {
                        Image(systemName: "questionmark.circle")
                            .font(.title2)
                            .foregroundColor(.green)
                        Text("FAQ")
                            .font(.headline)
                            .foregroundColor(.primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal)
                Button(action: {
                    showMailComposer = true
                }) {
                    HStack {
                        Image(systemName: "envelope")
                            .font(.title2)
                            .foregroundColor(.orange)
                        Text("Contact Us")
                            .font(.headline)
                            .foregroundColor(.primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal)
                Spacer()
            }
            .onAppear {
                fetchRecentRuns()
            }
            .sheet(isPresented: $showDatabaseTest) {
                DatabaseTestView()
            }
            .sheet(isPresented: $showFAQ) {
                FAQView()
            }
            .sheet(isPresented: $showAbout) {
                AboutView()
            }
            .sheet(isPresented: $showRunRecord) {
                WorkoutHistoryView()
            }
            .sheet(isPresented: $showMailComposer) {
                MailComposeView(
                    recipients: ["<EMAIL>"],
                    subject: "Cadence App Feedback",
                    body: ""
                )
            }
        }
    }
    
    private func fetchRecentRuns() {
        var descriptor = FetchDescriptor<RunRecord>(
            sortBy: [SortDescriptor(\.startTime, order: .reverse)]
        )
        descriptor.fetchLimit = 5
        do {
            recentRuns = try modelContext.fetch(descriptor)
        } catch {
            print("Failed to fetch recent runs: \(error)")
            recentRuns = []
        }
    }
}

// FAQ View
struct FAQView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "questionmark.circle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.blue)
                        
                        Text("Frequently Asked Questions")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)
                        
                        Text("Learn how to use Cadence effectively")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.bottom, 10)
                    
                    // FAQ Items
                    FAQItem(
                        question: "How do I start a run?",
                        answer: "Tap the play button on the main screen. The app will start tracking your location and playing a metronome to help you maintain a steady pace. Make sure location permissions are enabled for accurate tracking."
                    )
                    
                    FAQItem(
                        question: "What is the metronome for?",
                        answer: "The metronome helps you maintain a consistent running cadence (steps per minute). A good running cadence is typically between 160-180 steps per minute, which can help improve your running efficiency and reduce injury risk."
                    )
                    
                    FAQItem(
                        question: "How do I view my running history?",
                        answer: "Go to Settings and tap on 'History'. You'll see a list of all your past runs with details like distance, duration, and date. You can also see a preview of your recent runs right on the Settings page."
                    )
                    
                    FAQItem(
                        question: "Why do I need location permissions?",
                        answer: "Cadence uses GPS to track your running route and calculate distance accurately. The app requires 'Always' location permission to continue tracking even when the app is in the background."
                    )
                    
                    FAQItem(
                        question: "How accurate is the distance tracking?",
                        answer: "Distance is calculated using GPS coordinates and is generally accurate to within a few meters. For best results, ensure you have a clear view of the sky and wait for GPS to stabilize before starting your run."
                    )
                    
                    FAQItem(
                        question: "Can I use the app without the metronome?",
                        answer: "Yes! You can pause or stop the metronome during your run using the media controls. The GPS tracking will continue independently, so you'll still get accurate distance and time measurements."
                    )
                    
                    FAQItem(
                        question: "What happens if I minimize the app while running?",
                        answer: "The app will continue tracking your run in the background. The metronome will keep playing, and GPS tracking will continue. You'll see a banner at the top of your screen indicating active location tracking."
                    )
                    
                    FAQItem(
                        question: "How do I end a run?",
                        answer: "Tap the stop button (square icon) on the main screen. The app will show you a summary of your run including total distance, time, and average pace. Your run data will be automatically saved."
                    )
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

// FAQ Item Component
struct FAQItem: View {
    let question: String
    let answer: String
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Text(question)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            
            if isExpanded {
                Text(answer)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                    .padding(.vertical, 12)
                    .background(Color(.systemGray5))
                    .cornerRadius(12)
                    .padding(.top, 2)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
    }
}

// About View
struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("About")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()
                
                Text("About content will be added here")
                    .padding()
                
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// Mail Composer View
struct MailComposeView: UIViewControllerRepresentable {
    let recipients: [String]
    let subject: String
    let body: String
    @Environment(\.dismiss) private var dismiss
    
    func makeUIViewController(context: Context) -> MFMailComposeViewController {
        let mailComposer = MFMailComposeViewController()
        mailComposer.mailComposeDelegate = context.coordinator
        mailComposer.setToRecipients(recipients)
        mailComposer.setSubject(subject)
        mailComposer.setMessageBody(body, isHTML: false)
        return mailComposer
    }
    
    func updateUIViewController(_ uiViewController: MFMailComposeViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, MFMailComposeViewControllerDelegate {
        let parent: MailComposeView
        
        init(_ parent: MailComposeView) {
            self.parent = parent
        }
        
        func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?) {
            parent.dismiss()
        }
    }
}

#Preview {
    SettingsView()
}
