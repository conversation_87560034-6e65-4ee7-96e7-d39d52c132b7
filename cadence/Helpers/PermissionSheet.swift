//
//  PermissionSheet.swift
//  cadence
//
//  Created by dong yi on 2025/7/23.
//

import SwiftUI
import CoreLocation
import AVKit
import HealthKit
import PhotosUI
import MediaPlayer


/// permissionss
enum Permission: String, CaseIterable {
    case location = "Location"
    case music = "Music"
    case photos = "Photos"
    
    var symbol: String {
        switch self {
        case .location:
            return "location.fill"
        case .music:
            return "music.note"
        case .photos:
            return "photo.on.rectangle"
        }
    }
    
    var orderedIndex: Int {
        switch self {
        case .location:
            return 0
        case .music:
            return 1
        case .photos:
            return 2
        }
    }
    
    var isGranted: Bool? {
        switch self {
        case .location:
            let status = CLLocationManager().authorizationStatus
            return status == .authorizedAlways || status == .authorizedWhenInUse
        case .music:
            let status = MPMediaLibrary.authorizationStatus()
            return status == .authorized
        case .photos:
            let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
            return status == .notDetermined || status == .authorized || status == .limited
        }
    }
}

extension View {
    @ViewBuilder
    func permissionSheet(_ permissions: [Permission]) -> some View {
        self
            .modifier(PermissionSheetModifier(permissions: permissions))
    }
}

fileprivate struct PermissionSheetModifier: ViewModifier {
    init(permissions: [Permission]) {
        let initialStates = permissions.sorted(by: {
            $0.orderedIndex < $1.orderedIndex
        }).compactMap {
            PermissionState(id: $0)
        }
        
        self._states = .init(initialValue: initialStates)
    }
    
    /// view properties
    @State private var showSheet: Bool = false
    @State private var states: [PermissionState]
    @State private var currentIndex: Int = 0
    
    var locationManager = LocationManagerforPermission()
    @Environment(\.openURL) var openURL
    
    func body(content: Content) -> some View {
        content
            .sheet(isPresented: $showSheet) {
                VStack(spacing: 20) {
                    Text("Required Permissions")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Image(systemName: isAllGranted ? "person.badge.shield.checkmark" : "person.badge.shield.exclamationmark")
                        .font(.system(size: 60))
                        .foregroundStyle(.white)
                        .contentTransition(.symbolEffect(.replace))
                        .frame(width: 100, height: 100)
                        .background {
                            RoundedRectangle(cornerRadius: 30)
                                .fill(.blue.gradient)
                        }
                    
                    /// Permission Rows
                    VStack(alignment: .leading, spacing: 20) {
                        ForEach(states) { state in
                            permissionRow(state)
                                .contentShape(.rect)
                                .onTapGesture {
                                    requestPermission(state.id.orderedIndex)
                                }
                            
                        }
                    }
                    .padding(.top, 10)
                    
                    Spacer(minLength: 0)
                    
                    Button {
                        showSheet = false
                    } label: {
                        Text("start_using_app")
                            .fontWeight(.semibold)
                            .foregroundStyle(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(.blue.gradient, in: Capsule())
                    }
                    .disabled(!isAllGranted)
                    .opacity(isAllGranted ? 1 : 0.6)
                    .overlay(alignment: .top) {
                        if isThereAnyRejection {
                            Button("go_to_settings") {
                                if let appSettingURL = URL(string: UIApplication.openSettingsURLString) {
                                    openURL(appSettingURL)
                                }
                            }
                            .offset(y: -30)
                        }
                        
                    }
                    
                }
                .padding(.horizontal, 20)
                .padding(.top, 30)
                .presentationDetents([.height(480)])
                .interactiveDismissDisabled()
            }
            .onChange(of: locationManager.status) { oldValue, newValue in
                if let status = locationManager.status,
                   let index = states.firstIndex(where: { $0.id == .location }) {
                    
                    if status == .notDetermined {
                        showSheet = true
                        states[index].isGranted = nil
                        requestPermission(index)
                    } else if status == .denied || status == .restricted {
                        showSheet = true
                        states[index].isGranted = false
                    } else {
                        states[index].isGranted = (status == .authorizedAlways || status == .authorizedWhenInUse)
                    }
                }
            }
            .onChange(of: currentIndex) { oldValue, newValue in
                guard states[newValue].isGranted == nil else { return }
                requestPermission(newValue)
            }
            .onAppear {
                // Skip permission checks in preview mode
#if DEBUG
                if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
                    return
                }
#endif
                
                showSheet = !isAllGranted
                if let firstRequestPermission = states.firstIndex(where: { $0.isGranted == nil }) {
                    currentIndex = firstRequestPermission
                    requestPermission(firstRequestPermission)
                }
            }
    }
    
    @ViewBuilder
    private func permissionRow(_ state: PermissionState) -> some View {
        HStack(spacing: 10) {
            ZStack {
                Circle()
                    .stroke(.gray, lineWidth: 1)
                
                Group {
                    if let isGranted = state.isGranted {
                        Image(systemName: isGranted ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundStyle(isGranted ? .green : .red)
                    } else {
                        Image(systemName: "questionmark.circle.fill")
                            .foregroundStyle(.gray)
                    }
                }
            }
            .frame(width: 22, height: 22)
            
            Text(state.id.rawValue)
                .lineLimit(1)
        }
    }
    
    private func requestPermission(_ index: Int) {
        Task { @MainActor in
            let permission = states[index].id
            
            switch permission {
            case .location:
                locationManager.requestWhenInUseAuthorization()
            case .music:
                let status = MPMediaLibrary.authorizationStatus()
                states[index].isGranted = status == .authorized
            case .photos:
                let status = await PHPhotoLibrary.requestAuthorization(for: .readWrite)
                states[index].isGranted = status == .authorized || status == .limited
            }
            
            // Update current index to next permission
            currentIndex = min(currentIndex + 1, states.count - 1)
        }
        
    }
    
    private var isAllGranted: Bool {
        // states.allSatisfy { $0.isGranted == true }
        states.filter({
            if let isGranted = $0.isGranted {
                return isGranted
            }
            return false
        }).count == states.count
    }
    
    private var isThereAnyRejection: Bool {
        states.contains(where: { $0.isGranted == false })
    }
    
    
    private struct PermissionState: Identifiable {
        var id: Permission
        
        var isGranted: Bool?
        
        init(id: Permission) {
            self.id = id
            self.isGranted = id.isGranted
        }
        
    }
}

@MainActor
@Observable
fileprivate class LocationManagerforPermission: NSObject, @preconcurrency CLLocationManagerDelegate {
    var status: CLAuthorizationStatus?
    var manager = CLLocationManager()
    override init() {
        super.init()
        manager.delegate = self
    }
    
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        status = manager.authorizationStatus
    }
    
    func requestWhenInUseAuthorization() {
        manager.requestWhenInUseAuthorization()
    }
}


#Preview {
    ContentView()
}
